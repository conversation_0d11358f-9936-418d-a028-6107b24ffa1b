
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 5.22.0
 * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
 */
Prisma.prismaVersion = {
  client: "5.22.0",
  engine: "605197351a3c8bdd595af2d2a9bc3025bca48ea2"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.NotFoundError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`NotFoundError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  auth0Id: 'auth0Id',
  email: 'email',
  firstName: 'firstName',
  lastName: 'lastName',
  avatar: 'avatar',
  phone: 'phone',
  language: 'language',
  timezone: 'timezone',
  status: 'status',
  role: 'role',
  factoryId: 'factoryId',
  permissions: 'permissions',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastLoginAt: 'lastLoginAt'
};

exports.Prisma.FactoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  logo: 'logo',
  coverImage: 'coverImage',
  industry: 'industry',
  contactEmail: 'contactEmail',
  contactPhone: 'contactPhone',
  businessType: 'businessType',
  businessLicense: 'businessLicense',
  taxId: 'taxId',
  establishedYear: 'establishedYear',
  employeeCount: 'employeeCount',
  annualRevenue: 'annualRevenue',
  productCategories: 'productCategories',
  productionCapacity: 'productionCapacity',
  capacityUnit: 'capacityUnit',
  shippingMethods: 'shippingMethods',
  paymentMethods: 'paymentMethods',
  certifications: 'certifications',
  adminName: 'adminName',
  adminEmail: 'adminEmail',
  adminPhone: 'adminPhone',
  adminRole: 'adminRole',
  department: 'department',
  email: 'email',
  phone: 'phone',
  website: 'website',
  addressStreet: 'addressStreet',
  addressCity: 'addressCity',
  addressState: 'addressState',
  addressPostalCode: 'addressPostalCode',
  addressCountry: 'addressCountry',
  latitude: 'latitude',
  longitude: 'longitude',
  currency: 'currency',
  timezone: 'timezone',
  language: 'language',
  onboardingComplete: 'onboardingComplete',
  onboardingCompletedAt: 'onboardingCompletedAt',
  verificationStatus: 'verificationStatus',
  status: 'status',
  subscriptionTier: 'subscriptionTier',
  subscriptionEndsAt: 'subscriptionEndsAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  image: 'image',
  parentId: 'parentId',
  factoryId: 'factoryId',
  sortOrder: 'sortOrder',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  shortDescription: 'shortDescription',
  basePrice: 'basePrice',
  currency: 'currency',
  minOrderQty: 'minOrderQty',
  maxOrderQty: 'maxOrderQty',
  sku: 'sku',
  model: 'model',
  brand: 'brand',
  weight: 'weight',
  dimensions: 'dimensions',
  materials: 'materials',
  colors: 'colors',
  tags: 'tags',
  specifications: 'specifications',
  compliance: 'compliance',
  qualityData: 'qualityData',
  manufacturingData: 'manufacturingData',
  tradingData: 'tradingData',
  marketingData: 'marketingData',
  stockQuantity: 'stockQuantity',
  stockStatus: 'stockStatus',
  metaTitle: 'metaTitle',
  metaDescription: 'metaDescription',
  status: 'status',
  isActive: 'isActive',
  isFeatured: 'isFeatured',
  factoryId: 'factoryId',
  categoryId: 'categoryId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy'
};

exports.Prisma.ProductImageScalarFieldEnum = {
  id: 'id',
  url: 'url',
  alt: 'alt',
  caption: 'caption',
  sortOrder: 'sortOrder',
  isMain: 'isMain',
  uploadedAt: 'uploadedAt',
  fileSize: 'fileSize',
  mimeType: 'mimeType',
  originalName: 'originalName',
  altText: 'altText',
  status: 'status',
  productId: 'productId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ImageOperationScalarFieldEnum = {
  id: 'id',
  operationType: 'operationType',
  operationData: 'operationData',
  productId: 'productId',
  userId: 'userId',
  createdAt: 'createdAt'
};

exports.Prisma.ProductPriceBreakScalarFieldEnum = {
  id: 'id',
  minQuantity: 'minQuantity',
  unitPrice: 'unitPrice',
  currency: 'currency',
  productId: 'productId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductRegionalPricingScalarFieldEnum = {
  id: 'id',
  region: 'region',
  country: 'country',
  basePrice: 'basePrice',
  currency: 'currency',
  markup: 'markup',
  fixedAdjustment: 'fixedAdjustment',
  validFrom: 'validFrom',
  validUntil: 'validUntil',
  productId: 'productId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdBy: 'createdBy'
};

exports.Prisma.ProductSeasonalPricingScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  startDate: 'startDate',
  endDate: 'endDate',
  adjustmentType: 'adjustmentType',
  adjustmentValue: 'adjustmentValue',
  minQuantity: 'minQuantity',
  maxQuantity: 'maxQuantity',
  isActive: 'isActive',
  priority: 'priority',
  productId: 'productId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdBy: 'createdBy'
};

exports.Prisma.ProductCostBreakdownScalarFieldEnum = {
  id: 'id',
  materialCost: 'materialCost',
  laborCost: 'laborCost',
  overheadCost: 'overheadCost',
  packagingCost: 'packagingCost',
  shippingCost: 'shippingCost',
  marketingCost: 'marketingCost',
  targetMargin: 'targetMargin',
  actualMargin: 'actualMargin',
  totalCost: 'totalCost',
  suggestedPrice: 'suggestedPrice',
  currency: 'currency',
  quantityBasis: 'quantityBasis',
  validFrom: 'validFrom',
  validUntil: 'validUntil',
  productId: 'productId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdBy: 'createdBy'
};

exports.Prisma.ProductInventoryLocationScalarFieldEnum = {
  id: 'id',
  locationName: 'locationName',
  locationCode: 'locationCode',
  locationAddress: 'locationAddress',
  locationManager: 'locationManager',
  stockQuantity: 'stockQuantity',
  reservedQuantity: 'reservedQuantity',
  availableQuantity: 'availableQuantity',
  inTransitQuantity: 'inTransitQuantity',
  reorderPoint: 'reorderPoint',
  maxStockLevel: 'maxStockLevel',
  minStockLevel: 'minStockLevel',
  safetyStock: 'safetyStock',
  averageCost: 'averageCost',
  lastCost: 'lastCost',
  currency: 'currency',
  isActive: 'isActive',
  allowBackorders: 'allowBackorders',
  trackingEnabled: 'trackingEnabled',
  leadTime: 'leadTime',
  supplierLeadTime: 'supplierLeadTime',
  productId: 'productId',
  factoryId: 'factoryId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdBy: 'createdBy'
};

exports.Prisma.InventoryReservationScalarFieldEnum = {
  id: 'id',
  reservationType: 'reservationType',
  quantity: 'quantity',
  reservedUntil: 'reservedUntil',
  status: 'status',
  referenceId: 'referenceId',
  referenceType: 'referenceType',
  notes: 'notes',
  inventoryLocationId: 'inventoryLocationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdBy: 'createdBy'
};

exports.Prisma.InventoryMovementScalarFieldEnum = {
  id: 'id',
  movementType: 'movementType',
  quantity: 'quantity',
  previousQuantity: 'previousQuantity',
  newQuantity: 'newQuantity',
  reason: 'reason',
  notes: 'notes',
  batchNumber: 'batchNumber',
  expiryDate: 'expiryDate',
  unitCost: 'unitCost',
  totalCost: 'totalCost',
  currency: 'currency',
  referenceId: 'referenceId',
  referenceType: 'referenceType',
  fromLocationId: 'fromLocationId',
  toLocationId: 'toLocationId',
  inventoryLocationId: 'inventoryLocationId',
  createdAt: 'createdAt',
  createdBy: 'createdBy'
};

exports.Prisma.InventoryAlertScalarFieldEnum = {
  id: 'id',
  alertType: 'alertType',
  severity: 'severity',
  title: 'title',
  message: 'message',
  triggerValue: 'triggerValue',
  currentValue: 'currentValue',
  threshold: 'threshold',
  status: 'status',
  acknowledgedAt: 'acknowledgedAt',
  acknowledgedBy: 'acknowledgedBy',
  resolvedAt: 'resolvedAt',
  resolvedBy: 'resolvedBy',
  resolutionNotes: 'resolutionNotes',
  emailSent: 'emailSent',
  smseSent: 'smseSent',
  notificationSentAt: 'notificationSentAt',
  inventoryLocationId: 'inventoryLocationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductVariantScalarFieldEnum = {
  id: 'id',
  name: 'name',
  sku: 'sku',
  price: 'price',
  attributes: 'attributes',
  stockQuantity: 'stockQuantity',
  stockStatus: 'stockStatus',
  isActive: 'isActive',
  productId: 'productId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.QuoteScalarFieldEnum = {
  id: 'id',
  quoteNumber: 'quoteNumber',
  status: 'status',
  subtotal: 'subtotal',
  tax: 'tax',
  shipping: 'shipping',
  total: 'total',
  currency: 'currency',
  validUntil: 'validUntil',
  customerName: 'customerName',
  customerEmail: 'customerEmail',
  customerPhone: 'customerPhone',
  customerCompany: 'customerCompany',
  shippingAddress: 'shippingAddress',
  shippingMethod: 'shippingMethod',
  factoryId: 'factoryId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdBy: 'createdBy'
};

exports.Prisma.QuoteItemScalarFieldEnum = {
  id: 'id',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  totalPrice: 'totalPrice',
  notes: 'notes',
  productId: 'productId',
  variantId: 'variantId',
  quoteId: 'quoteId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrderScalarFieldEnum = {
  id: 'id',
  orderNumber: 'orderNumber',
  quoteId: 'quoteId',
  status: 'status',
  totalAmount: 'totalAmount',
  currency: 'currency',
  customerName: 'customerName',
  customerEmail: 'customerEmail',
  customerPhone: 'customerPhone',
  customerCompany: 'customerCompany',
  shippingStreet: 'shippingStreet',
  shippingCity: 'shippingCity',
  shippingState: 'shippingState',
  shippingPostalCode: 'shippingPostalCode',
  shippingCountry: 'shippingCountry',
  billingStreet: 'billingStreet',
  billingCity: 'billingCity',
  billingState: 'billingState',
  billingPostalCode: 'billingPostalCode',
  billingCountry: 'billingCountry',
  notes: 'notes',
  internalNotes: 'internalNotes',
  orderDate: 'orderDate',
  requiredDate: 'requiredDate',
  shippedDate: 'shippedDate',
  deliveredDate: 'deliveredDate',
  factoryId: 'factoryId',
  assignedToId: 'assignedToId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdBy: 'createdBy'
};

exports.Prisma.OrderItemScalarFieldEnum = {
  id: 'id',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  totalPrice: 'totalPrice',
  productId: 'productId',
  variantId: 'variantId',
  orderId: 'orderId',
  specifications: 'specifications',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrderStatusHistoryScalarFieldEnum = {
  id: 'id',
  status: 'status',
  notes: 'notes',
  orderId: 'orderId',
  createdAt: 'createdAt',
  createdBy: 'createdBy'
};

exports.Prisma.InquiryScalarFieldEnum = {
  id: 'id',
  subject: 'subject',
  message: 'message',
  status: 'status',
  customerName: 'customerName',
  customerEmail: 'customerEmail',
  customerPhone: 'customerPhone',
  customerCompany: 'customerCompany',
  productId: 'productId',
  factoryId: 'factoryId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ConversationScalarFieldEnum = {
  id: 'id',
  type: 'type',
  subject: 'subject',
  quoteId: 'quoteId',
  orderId: 'orderId',
  inquiryId: 'inquiryId',
  productId: 'productId',
  factoryId: 'factoryId',
  isActive: 'isActive',
  lastMessageAt: 'lastMessageAt',
  messageCount: 'messageCount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ConversationParticipantScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  conversationId: 'conversationId',
  role: 'role',
  canWrite: 'canWrite',
  canRead: 'canRead',
  lastReadAt: 'lastReadAt',
  unreadCount: 'unreadCount',
  notificationsEnabled: 'notificationsEnabled',
  joinedAt: 'joinedAt',
  leftAt: 'leftAt'
};

exports.Prisma.MessageScalarFieldEnum = {
  id: 'id',
  content: 'content',
  messageType: 'messageType',
  conversationId: 'conversationId',
  senderId: 'senderId',
  receiverId: 'receiverId',
  quoteId: 'quoteId',
  orderId: 'orderId',
  inquiryId: 'inquiryId',
  factoryId: 'factoryId',
  isRead: 'isRead',
  readAt: 'readAt',
  isEdited: 'isEdited',
  editedAt: 'editedAt',
  isDeleted: 'isDeleted',
  deletedAt: 'deletedAt',
  replyToId: 'replyToId',
  attachments: 'attachments',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MessageAttachmentScalarFieldEnum = {
  id: 'id',
  filename: 'filename',
  originalName: 'originalName',
  mimeType: 'mimeType',
  fileSize: 'fileSize',
  fileUrl: 'fileUrl',
  thumbnailUrl: 'thumbnailUrl',
  width: 'width',
  height: 'height',
  duration: 'duration',
  storageProvider: 'storageProvider',
  storagePath: 'storagePath',
  bucketName: 'bucketName',
  messageId: 'messageId',
  factoryId: 'factoryId',
  uploadStatus: 'uploadStatus',
  uploadProgress: 'uploadProgress',
  uploadError: 'uploadError',
  isPublic: 'isPublic',
  accessToken: 'accessToken',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MessageReadReceiptScalarFieldEnum = {
  id: 'id',
  messageId: 'messageId',
  userId: 'userId',
  readAt: 'readAt',
  factoryId: 'factoryId'
};

exports.Prisma.ProductReviewScalarFieldEnum = {
  id: 'id',
  rating: 'rating',
  title: 'title',
  comment: 'comment',
  reviewerName: 'reviewerName',
  reviewerEmail: 'reviewerEmail',
  reviewerCompany: 'reviewerCompany',
  productId: 'productId',
  isApproved: 'isApproved',
  isVisible: 'isVisible',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AuditLogScalarFieldEnum = {
  id: 'id',
  type: 'type',
  event: 'event',
  entityType: 'entityType',
  entityId: 'entityId',
  userId: 'userId',
  factoryId: 'factoryId',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  metadata: 'metadata',
  timestamp: 'timestamp'
};

exports.Prisma.NotificationScalarFieldEnum = {
  id: 'id',
  type: 'type',
  title: 'title',
  message: 'message',
  isRead: 'isRead',
  readAt: 'readAt',
  userId: 'userId',
  data: 'data',
  createdAt: 'createdAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.UserStatus = exports.$Enums.UserStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  SUSPENDED: 'SUSPENDED',
  PENDING_VERIFICATION: 'PENDING_VERIFICATION'
};

exports.UserRole = exports.$Enums.UserRole = {
  SUPER_ADMIN: 'SUPER_ADMIN',
  FACTORY_OWNER: 'FACTORY_OWNER',
  FACTORY_ADMIN: 'FACTORY_ADMIN',
  FACTORY_MANAGER: 'FACTORY_MANAGER',
  FACTORY_STAFF: 'FACTORY_STAFF',
  CUSTOMER: 'CUSTOMER',
  CUSTOMER_ADMIN: 'CUSTOMER_ADMIN'
};

exports.Currency = exports.$Enums.Currency = {
  USD: 'USD',
  EUR: 'EUR',
  GBP: 'GBP',
  CNY: 'CNY',
  JPY: 'JPY',
  KRW: 'KRW',
  CAD: 'CAD',
  AUD: 'AUD'
};

exports.VerificationStatus = exports.$Enums.VerificationStatus = {
  UNVERIFIED: 'UNVERIFIED',
  PENDING: 'PENDING',
  VERIFIED: 'VERIFIED',
  REJECTED: 'REJECTED'
};

exports.FactoryStatus = exports.$Enums.FactoryStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  SUSPENDED: 'SUSPENDED',
  PENDING_APPROVAL: 'PENDING_APPROVAL'
};

exports.SubscriptionTier = exports.$Enums.SubscriptionTier = {
  FREE: 'FREE',
  BASIC: 'BASIC',
  STANDARD: 'STANDARD',
  PREMIUM: 'PREMIUM',
  ENTERPRISE: 'ENTERPRISE'
};

exports.StockStatus = exports.$Enums.StockStatus = {
  IN_STOCK: 'IN_STOCK',
  LOW_STOCK: 'LOW_STOCK',
  OUT_OF_STOCK: 'OUT_OF_STOCK',
  BACKORDER: 'BACKORDER'
};

exports.ProductStatus = exports.$Enums.ProductStatus = {
  DRAFT: 'DRAFT',
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  OUT_OF_STOCK: 'OUT_OF_STOCK',
  DISCONTINUED: 'DISCONTINUED'
};

exports.ImageStatus = exports.$Enums.ImageStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  PROCESSING: 'PROCESSING',
  FAILED: 'FAILED',
  DELETED: 'DELETED'
};

exports.ImageOperationType = exports.$Enums.ImageOperationType = {
  ADD: 'ADD',
  REMOVE: 'REMOVE',
  REORDER: 'REORDER',
  SET_MAIN: 'SET_MAIN',
  UPDATE_ALT_TEXT: 'UPDATE_ALT_TEXT',
  UPDATE_METADATA: 'UPDATE_METADATA',
  BULK_UPLOAD: 'BULK_UPLOAD',
  BULK_DELETE: 'BULK_DELETE'
};

exports.ReservationType = exports.$Enums.ReservationType = {
  ORDER: 'ORDER',
  QUOTE: 'QUOTE',
  MANUAL: 'MANUAL',
  TRANSFER: 'TRANSFER',
  QUALITY_HOLD: 'QUALITY_HOLD'
};

exports.ReservationStatus = exports.$Enums.ReservationStatus = {
  ACTIVE: 'ACTIVE',
  EXPIRED: 'EXPIRED',
  FULFILLED: 'FULFILLED',
  CANCELLED: 'CANCELLED'
};

exports.MovementType = exports.$Enums.MovementType = {
  INBOUND: 'INBOUND',
  OUTBOUND: 'OUTBOUND',
  ADJUSTMENT: 'ADJUSTMENT',
  TRANSFER: 'TRANSFER',
  RETURN: 'RETURN',
  DAMAGED: 'DAMAGED',
  EXPIRED: 'EXPIRED'
};

exports.AlertType = exports.$Enums.AlertType = {
  LOW_STOCK: 'LOW_STOCK',
  OUT_OF_STOCK: 'OUT_OF_STOCK',
  OVERSTOCK: 'OVERSTOCK',
  REORDER_POINT: 'REORDER_POINT',
  EXPIRY_WARNING: 'EXPIRY_WARNING',
  SLOW_MOVING: 'SLOW_MOVING',
  FAST_MOVING: 'FAST_MOVING',
  COST_VARIANCE: 'COST_VARIANCE'
};

exports.AlertSeverity = exports.$Enums.AlertSeverity = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  CRITICAL: 'CRITICAL'
};

exports.AlertStatus = exports.$Enums.AlertStatus = {
  ACTIVE: 'ACTIVE',
  ACKNOWLEDGED: 'ACKNOWLEDGED',
  RESOLVED: 'RESOLVED',
  DISMISSED: 'DISMISSED'
};

exports.QuoteStatus = exports.$Enums.QuoteStatus = {
  PENDING: 'PENDING',
  SENT: 'SENT',
  ACCEPTED: 'ACCEPTED',
  REJECTED: 'REJECTED',
  EXPIRED: 'EXPIRED',
  CONVERTED: 'CONVERTED'
};

exports.OrderStatus = exports.$Enums.OrderStatus = {
  PENDING: 'PENDING',
  CONFIRMED: 'CONFIRMED',
  IN_PRODUCTION: 'IN_PRODUCTION',
  READY_TO_SHIP: 'READY_TO_SHIP',
  SHIPPED: 'SHIPPED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED',
  REFUNDED: 'REFUNDED'
};

exports.InquiryStatus = exports.$Enums.InquiryStatus = {
  OPEN: 'OPEN',
  IN_PROGRESS: 'IN_PROGRESS',
  RESOLVED: 'RESOLVED',
  CLOSED: 'CLOSED'
};

exports.ConversationType = exports.$Enums.ConversationType = {
  DIRECT: 'DIRECT',
  QUOTE: 'QUOTE',
  ORDER: 'ORDER',
  INQUIRY: 'INQUIRY',
  SUPPORT: 'SUPPORT',
  GROUP: 'GROUP'
};

exports.ParticipantRole = exports.$Enums.ParticipantRole = {
  OWNER: 'OWNER',
  ADMIN: 'ADMIN',
  MEMBER: 'MEMBER',
  READONLY: 'READONLY'
};

exports.MessageType = exports.$Enums.MessageType = {
  TEXT: 'TEXT',
  IMAGE: 'IMAGE',
  FILE: 'FILE',
  SYSTEM: 'SYSTEM',
  VOICE: 'VOICE',
  VIDEO: 'VIDEO',
  LOCATION: 'LOCATION',
  CONTACT: 'CONTACT'
};

exports.UploadStatus = exports.$Enums.UploadStatus = {
  PENDING: 'PENDING',
  UPLOADING: 'UPLOADING',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  CANCELLED: 'CANCELLED'
};

exports.AuditType = exports.$Enums.AuditType = {
  SECURITY: 'SECURITY',
  BUSINESS: 'BUSINESS',
  SYSTEM: 'SYSTEM'
};

exports.NotificationType = exports.$Enums.NotificationType = {
  INFO: 'INFO',
  SUCCESS: 'SUCCESS',
  WARNING: 'WARNING',
  ERROR: 'ERROR',
  ORDER_UPDATE: 'ORDER_UPDATE',
  MESSAGE: 'MESSAGE',
  SYSTEM: 'SYSTEM'
};

exports.Prisma.ModelName = {
  User: 'User',
  Factory: 'Factory',
  Category: 'Category',
  Product: 'Product',
  ProductImage: 'ProductImage',
  ImageOperation: 'ImageOperation',
  ProductPriceBreak: 'ProductPriceBreak',
  ProductRegionalPricing: 'ProductRegionalPricing',
  ProductSeasonalPricing: 'ProductSeasonalPricing',
  ProductCostBreakdown: 'ProductCostBreakdown',
  ProductInventoryLocation: 'ProductInventoryLocation',
  InventoryReservation: 'InventoryReservation',
  InventoryMovement: 'InventoryMovement',
  InventoryAlert: 'InventoryAlert',
  ProductVariant: 'ProductVariant',
  Quote: 'Quote',
  QuoteItem: 'QuoteItem',
  Order: 'Order',
  OrderItem: 'OrderItem',
  OrderStatusHistory: 'OrderStatusHistory',
  Inquiry: 'Inquiry',
  Conversation: 'Conversation',
  ConversationParticipant: 'ConversationParticipant',
  Message: 'Message',
  MessageAttachment: 'MessageAttachment',
  MessageReadReceipt: 'MessageReadReceipt',
  ProductReview: 'ProductReview',
  AuditLog: 'AuditLog',
  Notification: 'Notification'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }
        
        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
