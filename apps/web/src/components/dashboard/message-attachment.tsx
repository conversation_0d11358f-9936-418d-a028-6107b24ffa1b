'use client';

import { useState, useRef } from 'react';
import { Button } from '../ui/button';
import { Progress } from '../ui/progress';
import { Badge } from '../ui/badge';
import { 
  Paperclip, 
  File, 
  Image, 
  Video, 
  Music, 
  Download, 
  X,
  Upload
} from 'lucide-react';
import { trpc } from '../../lib/trpc';
import { LoadingSpinner } from '../ui/loading-spinner';

interface MessageAttachmentProps {
  onAttachmentUploaded?: (attachmentId: string) => void;
  maxFileSize?: number; // in MB
  allowedTypes?: string[];
}

interface AttachmentPreview {
  id: string;
  file: File;
  preview?: string;
  uploading: boolean;
  progress: number;
  error?: string;
}

export function MessageAttachment({ 
  onAttachmentUploaded,
  maxFileSize = 10,
  allowedTypes = ['image/*', 'application/pdf', 'text/*', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
}: MessageAttachmentProps) {
  const [attachments, setAttachments] = useState<AttachmentPreview[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Upload attachment mutation
  const uploadAttachmentMutation = trpc.messages.uploadAttachment.useMutation({
    onSuccess: (data, variables) => {
      const attachmentId = variables.filename.split('.')[0]; // Use filename as temp ID
      setAttachments(prev => prev.filter(a => a.id !== attachmentId));
      onAttachmentUploaded?.(data.id);
    },
    onError: (error, variables) => {
      const attachmentId = variables.filename.split('.')[0];
      setAttachments(prev => prev.map(a => 
        a.id === attachmentId 
          ? { ...a, uploading: false, error: error.message }
          : a
      ));
    },
  });

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;

    Array.from(files).forEach(file => {
      // Validate file size
      if (file.size > maxFileSize * 1024 * 1024) {
        alert(`File ${file.name} is too large. Maximum size is ${maxFileSize}MB.`);
        return;
      }

      // Validate file type
      const isAllowedType = allowedTypes.some(type => {
        if (type.endsWith('/*')) {
          return file.type.startsWith(type.slice(0, -1));
        }
        return file.type === type;
      });

      if (!isAllowedType) {
        alert(`File type ${file.type} is not allowed.`);
        return;
      }

      const attachmentId = `${Date.now()}-${Math.random()}`;
      const newAttachment: AttachmentPreview = {
        id: attachmentId,
        file,
        uploading: true,
        progress: 0,
      };

      // Create preview for images
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          setAttachments(prev => prev.map(a => 
            a.id === attachmentId 
              ? { ...a, preview: e.target?.result as string }
              : a
          ));
        };
        reader.readAsDataURL(file);
      }

      setAttachments(prev => [...prev, newAttachment]);

      // Simulate upload progress (replace with actual upload logic)
      const uploadFile = async () => {
        try {
          // Simulate progress
          for (let progress = 0; progress <= 100; progress += 10) {
            await new Promise(resolve => setTimeout(resolve, 100));
            setAttachments(prev => prev.map(a => 
              a.id === attachmentId 
                ? { ...a, progress }
                : a
            ));
          }

          // Upload to server
          await uploadAttachmentMutation.mutateAsync({
            filename: `${attachmentId}-${file.name}`,
            originalName: file.name,
            mimeType: file.type,
            fileSize: file.size,
            fileUrl: 'https://placeholder-url.com', // This would be the actual uploaded URL
            storagePath: `messages/${attachmentId}-${file.name}`,
            bucketName: 'messages',
          });
        } catch (error) {
          console.error('Upload failed:', error);
        }
      };

      uploadFile();
    });
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const removeAttachment = (id: string) => {
    setAttachments(prev => prev.filter(a => a.id !== id));
  };

  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) return <Image className="w-4 h-4" />;
    if (mimeType.startsWith('video/')) return <Video className="w-4 h-4" />;
    if (mimeType.startsWith('audio/')) return <Music className="w-4 h-4" />;
    return <File className="w-4 h-4" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          isDragOver 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <Upload className="w-8 h-8 mx-auto mb-2 text-gray-400" />
        <p className="text-sm text-gray-600 mb-2">
          Drag and drop files here, or{' '}
          <button
            onClick={() => fileInputRef.current?.click()}
            className="text-blue-500 hover:text-blue-600 underline"
          >
            browse
          </button>
        </p>
        <p className="text-xs text-gray-500">
          Maximum file size: {maxFileSize}MB
        </p>
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={allowedTypes.join(',')}
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
        />
      </div>

      {/* Attachment Previews */}
      {attachments.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Attachments</h4>
          {attachments.map(attachment => (
            <div
              key={attachment.id}
              className="flex items-center space-x-3 p-3 border rounded-lg bg-gray-50"
            >
              {/* File Icon/Preview */}
              <div className="flex-shrink-0">
                {attachment.preview ? (
                  <img
                    src={attachment.preview}
                    alt={attachment.file.name}
                    className="w-10 h-10 object-cover rounded"
                  />
                ) : (
                  <div className="w-10 h-10 bg-gray-200 rounded flex items-center justify-center">
                    {getFileIcon(attachment.file.type)}
                  </div>
                )}
              </div>

              {/* File Info */}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {attachment.file.name}
                </p>
                <p className="text-xs text-gray-500">
                  {formatFileSize(attachment.file.size)}
                </p>
                
                {/* Upload Progress */}
                {attachment.uploading && (
                  <div className="mt-2">
                    <Progress value={attachment.progress} className="h-1" />
                    <p className="text-xs text-gray-500 mt-1">
                      Uploading... {attachment.progress}%
                    </p>
                  </div>
                )}

                {/* Error */}
                {attachment.error && (
                  <p className="text-xs text-red-500 mt-1">
                    {attachment.error}
                  </p>
                )}
              </div>

              {/* Actions */}
              <div className="flex items-center space-x-2">
                {attachment.uploading ? (
                  <LoadingSpinner className="w-4 h-4" />
                ) : (
                  <Badge variant={attachment.error ? 'destructive' : 'secondary'} className="text-xs">
                    {attachment.error ? 'Failed' : 'Ready'}
                  </Badge>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeAttachment(attachment.id)}
                  className="h-6 w-6 p-0"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Quick Upload Button */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => fileInputRef.current?.click()}
        className="w-full"
      >
        <Paperclip className="w-4 h-4 mr-2" />
        Attach Files
      </Button>
    </div>
  );
}
