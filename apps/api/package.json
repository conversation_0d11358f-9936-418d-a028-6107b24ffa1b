{"name": "api", "version": "1.0.0", "description": "FC-CHINA tRPC API Server", "main": "src/server.ts", "scripts": {"build": "tsc", "dev": "tsx watch src/server.ts", "start": "node dist/server.js", "type-check": "tsc --noEmit", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx src/lib/database/seed.ts", "validate:env": "tsx src/lib/config/validate.ts", "clean": "rm -rf dist && rm -rf .turbo && rm -rf node_modules"}, "dependencies": {"@prisma/client": "^5.7.1", "@supabase/supabase-js": "^2.52.1", "@trpc/server": "^10.45.0", "auth0": "^4.2.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "prisma": "^5.7.1", "redis": "^4.6.12", "socket.io": "^4.8.1", "uuid": "^9.0.1", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.5", "@types/socket.io": "^3.0.1", "@types/uuid": "^9.0.7", "eslint": "^8.56.0", "ignore-loader": "^0.1.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}