// FC-CHINA Production Database Schema
// This schema is optimized for multi-tenant B2B manufacturing platform

generator client {
  provider = "prisma-client-js"
  output   = "../../../node_modules/.prisma/client"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// ============================================================================
// AUTHENTICATION & USER MANAGEMENT
// ============================================================================

model User {
  id          String   @id @default(cuid())
  auth0Id     String   @unique @map("auth0_id")
  email       String   @unique
  firstName   String   @map("first_name")
  lastName    String   @map("last_name")
  avatar      String?
  phone       String?
  language    String   @default("en")
  timezone    String   @default("UTC")
  status      UserStatus @default(ACTIVE)
  role        UserRole
  
  // Multi-tenant relationship
  factoryId   String?  @map("factory_id")
  factory     Factory? @relation(fields: [factoryId], references: [id], onDelete: Cascade)
  
  // Permissions
  permissions String[] @default([])
  
  // Audit fields
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  lastLoginAt DateTime? @map("last_login_at")
  
  // Relations
  createdProducts    Product[] @relation("ProductCreatedBy")
  updatedProducts    Product[] @relation("ProductUpdatedBy")
  imageOperations    ImageOperation[]
  createdQuotes      Quote[]   @relation("QuoteCreatedBy")
  createdOrders      Order[]   @relation("OrderCreatedBy")
  assignedOrders     Order[]   @relation("OrderAssignedTo")
  sentMessages       Message[] @relation("MessageSender")
  receivedMessages   Message[] @relation("MessageReceiver")
  conversationParticipants ConversationParticipant[]
  messageReadReceipts MessageReadReceipt[]
  auditLogs          AuditLog[]
  notifications      Notification[]
  
  @@map("users")
  @@index([auth0Id])
  @@index([email])
  @@index([factoryId])
  @@index([status])
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING_VERIFICATION
}

enum UserRole {
  SUPER_ADMIN
  FACTORY_OWNER
  FACTORY_ADMIN
  FACTORY_MANAGER
  FACTORY_STAFF
  CUSTOMER
  CUSTOMER_ADMIN
}

// ============================================================================
// FACTORY MANAGEMENT
// ============================================================================

model Factory {
  id                String   @id @default(cuid())
  name              String
  slug              String   @unique
  description       String?
  logo              String?
  coverImage        String?  @map("cover_image")

  // Basic Info (Step 1 of onboarding)
  industry          String?
  contactEmail      String?  @map("contact_email")
  contactPhone      String?  @map("contact_phone")

  // Business information (Step 2 of onboarding)
  businessType      String?  @map("business_type")
  businessLicense   String?  @map("business_license")
  taxId             String?  @map("tax_id")
  establishedYear   Int?     @map("established_year")
  employeeCount     String?  @map("employee_count") // Changed to String to match onboarding
  annualRevenue     Decimal? @map("annual_revenue") @db.Decimal(15, 2)

  // Factory Configuration (Step 3 of onboarding)
  productCategories Json?    @map("product_categories") // Array of categories
  productionCapacity String? @map("production_capacity")
  capacityUnit      String?  @map("capacity_unit")
  shippingMethods   Json?    @map("shipping_methods") // Array of shipping methods
  paymentMethods    Json?    @map("payment_methods") // Array of payment methods
  certifications    Json?    // Array of certifications

  // Admin Setup (Step 4 of onboarding)
  adminName         String?  @map("admin_name")
  adminEmail        String?  @map("admin_email")
  adminPhone        String?  @map("admin_phone")
  adminRole         String?  @map("admin_role")
  department        String?

  // Contact information (legacy fields)
  email             String
  phone             String?
  website           String?

  // Address
  addressStreet     String?  @map("address_street") // Made optional for onboarding
  addressCity       String?  @map("address_city") // Made optional for onboarding
  addressState      String?  @map("address_state") // Made optional for onboarding
  addressPostalCode String?  @map("address_postal_code") // Made optional for onboarding
  addressCountry    String?  @map("address_country") // Made optional for onboarding
  latitude          Float?
  longitude         Float?
  
  // Business settings
  currency          Currency @default(USD)
  timezone          String   @default("UTC")
  language          String   @default("en")
  
  // Onboarding and verification status
  onboardingComplete Boolean @default(false) @map("onboarding_complete")
  onboardingCompletedAt DateTime? @map("onboarding_completed_at")
  verificationStatus VerificationStatus @default(UNVERIFIED) @map("verification_status")
  status            FactoryStatus @default(PENDING_APPROVAL) // Changed default to PENDING_APPROVAL
  
  // Subscription
  subscriptionTier  SubscriptionTier @default(FREE) @map("subscription_tier")
  subscriptionEndsAt DateTime? @map("subscription_ends_at")
  
  // Audit fields
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")
  
  // Relations
  users             User[]
  products          Product[]
  categories        Category[]
  quotes            Quote[]
  orders            Order[]
  inquiries         Inquiry[]
  messages          Message[]
  conversations     Conversation[]
  messageAttachments MessageAttachment[]
  messageReadReceipts MessageReadReceipt[]
  inventoryLocations ProductInventoryLocation[]
  
  @@map("factories")
  @@index([slug])
  @@index([status])
  @@index([verificationStatus])
  @@index([subscriptionTier])
  @@index([addressCountry])
}

enum FactoryStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING_APPROVAL
}

enum VerificationStatus {
  UNVERIFIED
  PENDING
  VERIFIED
  REJECTED
}

enum SubscriptionTier {
  FREE
  BASIC
  STANDARD
  PREMIUM
  ENTERPRISE
}

enum Currency {
  USD
  EUR
  GBP
  CNY
  JPY
  KRW
  CAD
  AUD
}

// Inventory management enums
enum ReservationType {
  ORDER
  QUOTE
  MANUAL
  TRANSFER
  QUALITY_HOLD
}

enum ReservationStatus {
  ACTIVE
  EXPIRED
  FULFILLED
  CANCELLED
}

enum MovementType {
  INBOUND
  OUTBOUND
  ADJUSTMENT
  TRANSFER
  RETURN
  DAMAGED
  EXPIRED
}

enum AlertType {
  LOW_STOCK
  OUT_OF_STOCK
  OVERSTOCK
  REORDER_POINT
  EXPIRY_WARNING
  SLOW_MOVING
  FAST_MOVING
  COST_VARIANCE
}

enum AlertSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum AlertStatus {
  ACTIVE
  ACKNOWLEDGED
  RESOLVED
  DISMISSED
}

// ============================================================================
// PRODUCT MANAGEMENT
// ============================================================================

model Category {
  id          String   @id @default(cuid())
  name        String
  slug        String
  description String?
  image       String?
  parentId    String?  @map("parent_id")
  parent      Category? @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    Category[] @relation("CategoryHierarchy")
  
  // Multi-tenant
  factoryId   String   @map("factory_id")
  factory     Factory  @relation(fields: [factoryId], references: [id], onDelete: Cascade)
  
  // Metadata
  sortOrder   Int      @default(0) @map("sort_order")
  isActive    Boolean  @default(true) @map("is_active")
  
  // Audit fields
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  
  // Relations
  products    Product[]
  
  @@map("categories")
  @@unique([factoryId, slug])
  @@index([factoryId])
  @@index([parentId])
  @@index([isActive])
}

model Product {
  id              String   @id @default(cuid())
  name            String
  slug            String
  description     String?
  shortDescription String? @map("short_description")
  
  // Pricing
  basePrice       Decimal  @map("base_price") @db.Decimal(10, 2)
  currency        Currency @default(USD)
  minOrderQty     Int      @default(1) @map("min_order_qty")
  maxOrderQty     Int?     @map("max_order_qty")
  
  // Product details
  sku             String?
  model           String?
  brand           String?
  weight          Decimal? @db.Decimal(8, 3)
  dimensions      Json?    // {length, width, height, unit, weightUnit}
  materials       String[] @default([])
  colors          String[] @default([])
  tags            String[] @default([])

  // Enterprise-level specifications (JSONB for flexibility)
  specifications  Json?    // Manufacturing details, technical specs, customization options
  compliance      Json?    // Certifications, HS codes, export restrictions, country of origin
  qualityData     Json?    // Quality grade, defect rate, testing protocols, supplier info
  manufacturingData Json?  // Production capacity, lead time, manufacturing process
  tradingData     Json?    // Price breaks, regional pricing, seasonal adjustments
  marketingData   Json?    // SEO, positioning, competitive advantages

  // Inventory
  stockQuantity   Int      @default(0) @map("stock_quantity")
  stockStatus     StockStatus @default(IN_STOCK) @map("stock_status")

  // SEO and metadata
  metaTitle       String?  @map("meta_title")
  metaDescription String?  @map("meta_description")
  
  // Status and visibility
  status          ProductStatus @default(DRAFT)
  isActive        Boolean  @default(true) @map("is_active")
  isFeatured      Boolean  @default(false) @map("is_featured")
  
  // Multi-tenant
  factoryId       String   @map("factory_id")
  factory         Factory  @relation(fields: [factoryId], references: [id], onDelete: Cascade)
  
  // Category
  categoryId      String   @map("category_id")
  category        Category @relation(fields: [categoryId], references: [id])
  
  // Audit fields
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")
  createdBy       String?  @map("created_by")
  updatedBy       String?  @map("updated_by")
  createdByUser   User?    @relation("ProductCreatedBy", fields: [createdBy], references: [id])
  updatedByUser   User?    @relation("ProductUpdatedBy", fields: [updatedBy], references: [id])
  
  // Relations
  images          ProductImage[]
  imageOperations ImageOperation[]
  variants        ProductVariant[]
  quoteItems      QuoteItem[]
  orderItems      OrderItem[]
  inquiries       Inquiry[]
  reviews         ProductReview[]
  priceBreaks     ProductPriceBreak[]
  inventoryLocations ProductInventoryLocation[]
  regionalPricing ProductRegionalPricing[]
  seasonalPricing ProductSeasonalPricing[]
  costBreakdowns  ProductCostBreakdown[]
  conversations   Conversation[] @relation("ProductConversation")
  
  @@map("products")
  @@unique([factoryId, slug])
  @@index([factoryId])
  @@index([categoryId])
  @@index([status])
  @@index([isActive])
  @@index([isFeatured])
  @@index([stockStatus])
  @@index([createdAt])
}

enum ProductStatus {
  DRAFT
  ACTIVE
  INACTIVE
  OUT_OF_STOCK
  DISCONTINUED
}

enum StockStatus {
  IN_STOCK
  LOW_STOCK
  OUT_OF_STOCK
  BACKORDER
}

enum ImageStatus {
  ACTIVE
  INACTIVE
  PROCESSING
  FAILED
  DELETED
}

enum ImageOperationType {
  ADD
  REMOVE
  REORDER
  SET_MAIN
  UPDATE_ALT_TEXT
  UPDATE_METADATA
  BULK_UPLOAD
  BULK_DELETE
}

model ProductImage {
  id        String   @id @default(cuid())
  url       String
  alt       String?
  caption   String?
  sortOrder Int      @default(0) @map("sort_order")
  isMain    Boolean  @default(false) @map("is_main")

  // Enhanced image metadata fields
  uploadedAt   DateTime? @map("uploaded_at")
  fileSize     Int?      @map("file_size") // Size in bytes
  mimeType     String?   @map("mime_type") // e.g., 'image/jpeg', 'image/png'
  originalName String?   @map("original_name") // Original filename
  altText      String?   @map("alt_text") // Accessibility alt text (separate from alt field for backward compatibility)
  status       ImageStatus @default(ACTIVE) // Image status for soft deletion and management

  // Product relationship
  productId String   @map("product_id")
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  // Audit fields
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("product_images")
  @@index([productId])
  @@index([isMain])
  @@index([status])
  @@index([uploadedAt])
}

// Image operation audit table for tracking all image management operations
model ImageOperation {
  id            String   @id @default(cuid())
  operationType ImageOperationType @map("operation_type")
  operationData Json     @map("operation_data") // Flexible JSON field for operation-specific data

  // Relationships
  productId String  @map("product_id")
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  userId String? @map("user_id") // Optional - for system operations
  user   User?   @relation(fields: [userId], references: [id], onDelete: SetNull)

  // Audit fields
  createdAt DateTime @default(now()) @map("created_at")

  @@map("image_operations")
  @@index([productId])
  @@index([operationType])
  @@index([userId])
  @@index([createdAt])
}

// Product price breaks for volume pricing
model ProductPriceBreak {
  id          String  @id @default(cuid())
  minQuantity Int     @map("min_quantity")
  unitPrice   Decimal @map("unit_price") @db.Decimal(10, 2)
  currency    Currency @default(USD)

  // Product relationship
  productId   String  @map("product_id")
  product     Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  // Audit fields
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("product_price_breaks")
  @@index([productId])
  @@index([minQuantity])
}

// Regional pricing for different markets
model ProductRegionalPricing {
  id          String  @id @default(cuid())
  region      String  // e.g., "US", "EU", "APAC", "LATAM"
  country     String? // Specific country code (optional, for country-specific pricing)
  basePrice   Decimal @map("base_price") @db.Decimal(10, 2)
  currency    Currency @default(USD)

  // Pricing adjustments
  markup      Decimal? @db.Decimal(5, 2) // Percentage markup/discount
  fixedAdjustment Decimal? @map("fixed_adjustment") @db.Decimal(10, 2) // Fixed amount adjustment

  // Validity period
  validFrom   DateTime @map("valid_from")
  validUntil  DateTime? @map("valid_until")

  // Product relationship
  productId   String  @map("product_id")
  product     Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  // Audit fields
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  createdBy   String?  @map("created_by")

  @@map("product_regional_pricing")
  @@index([productId])
  @@index([region])
  @@index([country])
  @@index([validFrom, validUntil])
}

// Seasonal pricing adjustments
model ProductSeasonalPricing {
  id          String  @id @default(cuid())
  name        String  // e.g., "Holiday Season", "Back to School", "Black Friday"
  description String?

  // Seasonal period
  startDate   DateTime @map("start_date")
  endDate     DateTime @map("end_date")

  // Pricing adjustment
  adjustmentType String @map("adjustment_type") // "PERCENTAGE" or "FIXED"
  adjustmentValue Decimal @map("adjustment_value") @db.Decimal(10, 2)

  // Conditions
  minQuantity Int? @map("min_quantity") // Minimum quantity for seasonal pricing
  maxQuantity Int? @map("max_quantity") // Maximum quantity for seasonal pricing

  // Status
  isActive    Boolean @default(true) @map("is_active")
  priority    Int     @default(0) // Higher priority overrides lower priority

  // Product relationship
  productId   String  @map("product_id")
  product     Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  // Audit fields
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  createdBy   String?  @map("created_by")

  @@map("product_seasonal_pricing")
  @@index([productId])
  @@index([startDate, endDate])
  @@index([isActive])
  @@index([priority])
}

// Cost breakdown analysis
model ProductCostBreakdown {
  id          String  @id @default(cuid())

  // Material costs
  materialCost Decimal @map("material_cost") @db.Decimal(10, 2)
  laborCost   Decimal @map("labor_cost") @db.Decimal(10, 2)
  overheadCost Decimal @map("overhead_cost") @db.Decimal(10, 2)

  // Additional costs
  packagingCost Decimal? @map("packaging_cost") @db.Decimal(10, 2)
  shippingCost Decimal? @map("shipping_cost") @db.Decimal(10, 2)
  marketingCost Decimal? @map("marketing_cost") @db.Decimal(10, 2)

  // Margins
  targetMargin Decimal @map("target_margin") @db.Decimal(5, 2) // Percentage
  actualMargin Decimal? @map("actual_margin") @db.Decimal(5, 2) // Calculated margin

  // Total costs
  totalCost   Decimal @map("total_cost") @db.Decimal(10, 2)
  suggestedPrice Decimal @map("suggested_price") @db.Decimal(10, 2)

  // Currency and quantity basis
  currency    Currency @default(USD)
  quantityBasis Int @map("quantity_basis") @default(1) // Cost per X units

  // Validity
  validFrom   DateTime @map("valid_from")
  validUntil  DateTime? @map("valid_until")

  // Product relationship
  productId   String  @map("product_id")
  product     Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  // Audit fields
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  createdBy   String?  @map("created_by")

  @@map("product_cost_breakdown")
  @@index([productId])
  @@index([validFrom, validUntil])
}

// Multi-location inventory management
model ProductInventoryLocation {
  id               String  @id @default(cuid())
  locationName     String  @map("location_name")
  locationCode     String? @map("location_code") // Unique identifier for location
  locationAddress  String? @map("location_address")
  locationManager  String? @map("location_manager")

  // Stock levels
  stockQuantity    Int     @default(0) @map("stock_quantity")
  reservedQuantity Int     @default(0) @map("reserved_quantity")
  availableQuantity Int    @default(0) @map("available_quantity") // Computed: stock - reserved
  inTransitQuantity Int    @default(0) @map("in_transit_quantity")

  // Inventory thresholds
  reorderPoint     Int?    @map("reorder_point")
  maxStockLevel    Int?    @map("max_stock_level")
  minStockLevel    Int?    @map("min_stock_level")
  safetyStock      Int?    @map("safety_stock")

  // Cost tracking
  averageCost      Decimal? @db.Decimal(10, 2) @map("average_cost")
  lastCost         Decimal? @db.Decimal(10, 2) @map("last_cost")
  currency         Currency @default(USD)

  // Status and settings
  isActive         Boolean @default(true) @map("is_active")
  allowBackorders  Boolean @default(false) @map("allow_backorders")
  trackingEnabled  Boolean @default(true) @map("tracking_enabled")

  // Lead times (in days)
  leadTime         Int?    @map("lead_time")
  supplierLeadTime Int?    @map("supplier_lead_time")

  // Product relationship
  productId        String  @map("product_id")
  product          Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  // Factory relationship for multi-tenant isolation
  factoryId        String  @map("factory_id")
  factory          Factory @relation(fields: [factoryId], references: [id], onDelete: Cascade)

  // Relationships
  reservations     InventoryReservation[]
  movements        InventoryMovement[]
  alerts           InventoryAlert[]

  // Audit fields
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")
  createdBy        String?  @map("created_by")

  @@map("product_inventory_locations")
  @@unique([productId, locationCode, factoryId])
  @@index([productId])
  @@index([locationName])
  @@index([stockQuantity])
  @@index([factoryId])
  @@index([isActive])
}

// Inventory reservations for orders and quotes
model InventoryReservation {
  id               String   @id @default(cuid())
  reservationType  ReservationType @map("reservation_type")
  quantity         Int
  reservedUntil    DateTime @map("reserved_until")
  status           ReservationStatus @default(ACTIVE)

  // Reference information
  referenceId      String?  @map("reference_id") // Order ID, Quote ID, etc.
  referenceType    String?  @map("reference_type") // "ORDER", "QUOTE", "MANUAL"
  notes            String?

  // Relationships
  inventoryLocationId String @map("inventory_location_id")
  inventoryLocation   ProductInventoryLocation @relation(fields: [inventoryLocationId], references: [id], onDelete: Cascade)

  // Audit fields
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")
  createdBy        String?  @map("created_by")

  @@map("inventory_reservations")
  @@index([inventoryLocationId])
  @@index([status])
  @@index([reservedUntil])
  @@index([referenceId, referenceType])
}

// Inventory movement tracking
model InventoryMovement {
  id               String   @id @default(cuid())
  movementType     MovementType @map("movement_type")
  quantity         Int      // Positive for inbound, negative for outbound
  previousQuantity Int      @map("previous_quantity")
  newQuantity      Int      @map("new_quantity")

  // Movement details
  reason           String?  // "PURCHASE", "SALE", "ADJUSTMENT", "TRANSFER", "RETURN"
  notes            String?
  batchNumber      String?  @map("batch_number")
  expiryDate       DateTime? @map("expiry_date")

  // Cost information
  unitCost         Decimal? @db.Decimal(10, 2) @map("unit_cost")
  totalCost        Decimal? @db.Decimal(10, 2) @map("total_cost")
  currency         Currency @default(USD)

  // Reference information
  referenceId      String?  @map("reference_id")
  referenceType    String?  @map("reference_type")

  // Transfer information (for location transfers)
  fromLocationId   String?  @map("from_location_id")
  toLocationId     String?  @map("to_location_id")

  // Relationships
  inventoryLocationId String @map("inventory_location_id")
  inventoryLocation   ProductInventoryLocation @relation(fields: [inventoryLocationId], references: [id], onDelete: Cascade)

  // Audit fields
  createdAt        DateTime @default(now()) @map("created_at")
  createdBy        String?  @map("created_by")

  @@map("inventory_movements")
  @@index([inventoryLocationId])
  @@index([movementType])
  @@index([createdAt])
  @@index([referenceId, referenceType])
}

// Inventory alerts and notifications
model InventoryAlert {
  id               String   @id @default(cuid())
  alertType        AlertType @map("alert_type")
  severity         AlertSeverity @default(MEDIUM)
  title            String
  message          String

  // Alert conditions
  triggerValue     Int?     @map("trigger_value")
  currentValue     Int?     @map("current_value")
  threshold        Int?

  // Status and resolution
  status           AlertStatus @default(ACTIVE)
  acknowledgedAt   DateTime? @map("acknowledged_at")
  acknowledgedBy   String?   @map("acknowledged_by")
  resolvedAt       DateTime? @map("resolved_at")
  resolvedBy       String?   @map("resolved_by")
  resolutionNotes  String?   @map("resolution_notes")

  // Notification settings
  emailSent        Boolean  @default(false) @map("email_sent")
  smseSent         Boolean  @default(false) @map("sms_sent")
  notificationSentAt DateTime? @map("notification_sent_at")

  // Relationships
  inventoryLocationId String @map("inventory_location_id")
  inventoryLocation   ProductInventoryLocation @relation(fields: [inventoryLocationId], references: [id], onDelete: Cascade)

  // Audit fields
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  @@map("inventory_alerts")
  @@index([inventoryLocationId])
  @@index([alertType])
  @@index([status])
  @@index([severity])
  @@index([createdAt])
}

model ProductVariant {
  id          String   @id @default(cuid())
  name        String
  sku         String?
  price       Decimal  @db.Decimal(10, 2)
  
  // Variant attributes
  attributes  Json     // {color: "red", size: "large", etc.}
  
  // Inventory
  stockQuantity Int    @default(0) @map("stock_quantity")
  stockStatus   StockStatus @default(IN_STOCK) @map("stock_status")
  
  // Status
  isActive    Boolean  @default(true) @map("is_active")
  
  // Product relationship
  productId   String   @map("product_id")
  product     Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  
  // Audit fields
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  
  // Relations
  quoteItems  QuoteItem[]
  orderItems  OrderItem[]
  
  @@map("product_variants")
  @@unique([productId, sku])
  @@index([productId])
  @@index([isActive])
}

// ============================================================================
// QUOTE MANAGEMENT
// ============================================================================

model Quote {
  id              String      @id @default(cuid())
  quoteNumber     String      @unique @map("quote_number")

  // Quote details
  status          QuoteStatus @default(PENDING)
  subtotal        Decimal     @db.Decimal(12, 2)
  tax             Decimal?    @db.Decimal(10, 2)
  shipping        Decimal?    @db.Decimal(10, 2)
  total           Decimal     @db.Decimal(12, 2)
  currency        Currency    @default(USD)

  // Validity
  validUntil      DateTime    @map("valid_until")

  // Customer information
  customerName    String      @map("customer_name")
  customerEmail   String      @map("customer_email")
  customerPhone   String?     @map("customer_phone")
  customerCompany String?     @map("customer_company")

  // Shipping information
  shippingAddress Json?       @map("shipping_address")
  shippingMethod  String?     @map("shipping_method")

  // Multi-tenant
  factoryId       String      @map("factory_id")
  factory         Factory     @relation(fields: [factoryId], references: [id], onDelete: Cascade)

  // Audit fields
  createdAt       DateTime    @default(now()) @map("created_at")
  updatedAt       DateTime    @updatedAt @map("updated_at")
  createdBy       String?     @map("created_by")
  createdByUser   User?       @relation("QuoteCreatedBy", fields: [createdBy], references: [id])

  // Relations
  items           QuoteItem[]
  order           Order?      // One-to-one when quote becomes order
  messages        Message[]   @relation("QuoteMessages")
  conversation    Conversation? @relation("QuoteConversation")

  @@map("quotes")
  @@index([factoryId])
  @@index([status])
  @@index([customerEmail])
  @@index([createdAt])
}

model QuoteItem {
  id            String   @id @default(cuid())
  quantity      Int
  unitPrice     Decimal  @map("unit_price") @db.Decimal(10, 2)
  totalPrice    Decimal  @map("total_price") @db.Decimal(12, 2)
  notes         String?

  // Product relationship
  productId     String   @map("product_id")
  product       Product  @relation(fields: [productId], references: [id])

  // Variant relationship (optional)
  variantId     String?  @map("variant_id")
  variant       ProductVariant? @relation(fields: [variantId], references: [id])

  // Quote relationship
  quoteId       String   @map("quote_id")
  quote         Quote    @relation(fields: [quoteId], references: [id], onDelete: Cascade)

  // Audit fields
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  @@map("quote_items")
  @@index([quoteId])
  @@index([productId])
}

enum QuoteStatus {
  PENDING
  SENT
  ACCEPTED
  REJECTED
  EXPIRED
  CONVERTED
}

// ============================================================================
// ORDER MANAGEMENT
// ============================================================================

model Order {
  id              String   @id @default(cuid())
  orderNumber     String   @unique @map("order_number")

  // Quote relationship (optional - orders can be created from quotes or directly)
  quoteId         String?  @unique @map("quote_id")
  quote           Quote?   @relation(fields: [quoteId], references: [id])

  // Order details
  status          OrderStatus @default(PENDING)
  totalAmount     Decimal  @map("total_amount") @db.Decimal(12, 2)
  currency        Currency @default(USD)

  // Customer information
  customerName    String   @map("customer_name")
  customerEmail   String   @map("customer_email")
  customerPhone   String?  @map("customer_phone")
  customerCompany String?  @map("customer_company")

  // Shipping address
  shippingStreet     String   @map("shipping_street")
  shippingCity       String   @map("shipping_city")
  shippingState      String   @map("shipping_state")
  shippingPostalCode String   @map("shipping_postal_code")
  shippingCountry    String   @map("shipping_country")

  // Billing address (optional, defaults to shipping)
  billingStreet      String?  @map("billing_street")
  billingCity        String?  @map("billing_city")
  billingState       String?  @map("billing_state")
  billingPostalCode  String?  @map("billing_postal_code")
  billingCountry     String?  @map("billing_country")

  // Order metadata
  notes           String?
  internalNotes   String?  @map("internal_notes")

  // Dates
  orderDate       DateTime @default(now()) @map("order_date")
  requiredDate    DateTime? @map("required_date")
  shippedDate     DateTime? @map("shipped_date")
  deliveredDate   DateTime? @map("delivered_date")

  // Multi-tenant
  factoryId       String   @map("factory_id")
  factory         Factory  @relation(fields: [factoryId], references: [id], onDelete: Cascade)

  // Assignment
  assignedToId    String?  @map("assigned_to_id")
  assignedTo      User?    @relation("OrderAssignedTo", fields: [assignedToId], references: [id])

  // Audit fields
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")
  createdBy       String   @map("created_by")
  createdByUser   User     @relation("OrderCreatedBy", fields: [createdBy], references: [id])

  // Relations
  items           OrderItem[]
  messages        Message[]
  statusHistory   OrderStatusHistory[]
  conversation    Conversation? @relation("OrderConversation")

  @@map("orders")
  @@index([factoryId])
  @@index([status])
  @@index([orderDate])
  @@index([customerEmail])
}

enum OrderStatus {
  PENDING
  CONFIRMED
  IN_PRODUCTION
  READY_TO_SHIP
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

model OrderItem {
  id            String   @id @default(cuid())
  quantity      Int
  unitPrice     Decimal  @map("unit_price") @db.Decimal(10, 2)
  totalPrice    Decimal  @map("total_price") @db.Decimal(12, 2)

  // Product relationship
  productId     String   @map("product_id")
  product       Product  @relation(fields: [productId], references: [id])

  // Variant relationship (optional)
  variantId     String?  @map("variant_id")
  variant       ProductVariant? @relation(fields: [variantId], references: [id])

  // Order relationship
  orderId       String   @map("order_id")
  order         Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)

  // Item specifications
  specifications Json?   // Custom specifications for this order item

  // Audit fields
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  @@map("order_items")
  @@index([orderId])
  @@index([productId])
}

model OrderStatusHistory {
  id        String      @id @default(cuid())
  status    OrderStatus
  notes     String?

  // Order relationship
  orderId   String      @map("order_id")
  order     Order       @relation(fields: [orderId], references: [id], onDelete: Cascade)

  // Audit fields
  createdAt DateTime    @default(now()) @map("created_at")
  createdBy String      @map("created_by")

  @@map("order_status_history")
  @@index([orderId])
  @@index([createdAt])
}

// ============================================================================
// COMMUNICATION & MESSAGING
// ============================================================================

model Inquiry {
  id            String   @id @default(cuid())
  subject       String
  message       String
  status        InquiryStatus @default(OPEN)

  // Customer information
  customerName  String   @map("customer_name")
  customerEmail String   @map("customer_email")
  customerPhone String?  @map("customer_phone")
  customerCompany String? @map("customer_company")

  // Product relationship (optional)
  productId     String?  @map("product_id")
  product       Product? @relation(fields: [productId], references: [id])

  // Factory relationship
  factoryId     String   @map("factory_id")
  factory       Factory  @relation(fields: [factoryId], references: [id], onDelete: Cascade)

  // Audit fields
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // Relations
  messages      Message[]
  conversation  Conversation? @relation("InquiryConversation")

  @@map("inquiries")
  @@index([factoryId])
  @@index([status])
  @@index([customerEmail])
  @@index([createdAt])
}

enum InquiryStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
}

// ============================================================================
// MESSAGING SYSTEM
// ============================================================================

model Conversation {
  id              String   @id @default(cuid())

  // Conversation type and context
  type            ConversationType @default(DIRECT)
  subject         String?  // Optional subject for the conversation

  // Context relationships (one of these may be set)
  quoteId         String?  @unique @map("quote_id")
  quote           Quote?   @relation("QuoteConversation", fields: [quoteId], references: [id], onDelete: Cascade)
  orderId         String?  @unique @map("order_id")
  order           Order?   @relation("OrderConversation", fields: [orderId], references: [id], onDelete: Cascade)
  inquiryId       String?  @unique @map("inquiry_id")
  inquiry         Inquiry? @relation("InquiryConversation", fields: [inquiryId], references: [id], onDelete: Cascade)
  productId       String?  @map("product_id")
  product         Product? @relation("ProductConversation", fields: [productId], references: [id], onDelete: Cascade)

  // Multi-tenant isolation
  factoryId       String   @map("factory_id")
  factory         Factory  @relation(fields: [factoryId], references: [id], onDelete: Cascade)

  // Conversation metadata
  isActive        Boolean  @default(true) @map("is_active")
  lastMessageAt   DateTime? @map("last_message_at")
  messageCount    Int      @default(0) @map("message_count")

  // Audit fields
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // Relations
  messages        Message[]
  participants    ConversationParticipant[]

  @@map("conversations")
  @@index([factoryId])
  @@index([type])
  @@index([isActive])
  @@index([lastMessageAt])
  @@index([quoteId])
  @@index([orderId])
  @@index([inquiryId])
  @@index([productId])
}

enum ConversationType {
  DIRECT          // Direct message between two users
  QUOTE           // Quote-related conversation
  ORDER           // Order-related conversation
  INQUIRY         // Product inquiry conversation
  SUPPORT         // Customer support conversation
  GROUP           // Group conversation (future use)
}

model ConversationParticipant {
  id              String   @id @default(cuid())

  // Participant details
  userId          String   @map("user_id")
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  conversationId  String   @map("conversation_id")
  conversation    Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  // Participant role and permissions
  role            ParticipantRole @default(MEMBER)
  canWrite        Boolean  @default(true) @map("can_write")
  canRead         Boolean  @default(true) @map("can_read")

  // Read status tracking
  lastReadAt      DateTime? @map("last_read_at")
  unreadCount     Int      @default(0) @map("unread_count")

  // Notification preferences
  notificationsEnabled Boolean @default(true) @map("notifications_enabled")

  // Audit fields
  joinedAt        DateTime @default(now()) @map("joined_at")
  leftAt          DateTime? @map("left_at")

  @@map("conversation_participants")
  @@unique([conversationId, userId])
  @@index([userId])
  @@index([conversationId])
  @@index([lastReadAt])
}

enum ParticipantRole {
  OWNER           // Conversation owner (can manage participants)
  ADMIN           // Can manage conversation settings
  MEMBER          // Regular participant
  READONLY        // Can only read messages
}

model Message {
  id              String   @id @default(cuid())
  content         String
  messageType     MessageType @default(TEXT) @map("message_type")

  // Conversation relationship
  conversationId  String   @map("conversation_id")
  conversation    Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  // Sender information
  senderId        String   @map("sender_id")
  sender          User     @relation("MessageSender", fields: [senderId], references: [id])

  // Legacy receiver field (for backward compatibility)
  receiverId      String?  @map("receiver_id")
  receiver        User?    @relation("MessageReceiver", fields: [receiverId], references: [id])

  // Legacy context relationships (for backward compatibility)
  quoteId         String?  @map("quote_id")
  quote           Quote?   @relation("QuoteMessages", fields: [quoteId], references: [id], onDelete: Cascade)
  orderId         String?  @map("order_id")
  order           Order?   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  inquiryId       String?  @map("inquiry_id")
  inquiry         Inquiry? @relation(fields: [inquiryId], references: [id], onDelete: Cascade)

  // Multi-tenant isolation
  factoryId       String   @map("factory_id")
  factory         Factory  @relation(fields: [factoryId], references: [id], onDelete: Cascade)

  // Message metadata
  isRead          Boolean  @default(false) @map("is_read")
  readAt          DateTime? @map("read_at")
  isEdited        Boolean  @default(false) @map("is_edited")
  editedAt        DateTime? @map("edited_at")
  isDeleted       Boolean  @default(false) @map("is_deleted")
  deletedAt       DateTime? @map("deleted_at")

  // Reply functionality
  replyToId       String?  @map("reply_to_id")
  replyTo         Message? @relation("MessageReplies", fields: [replyToId], references: [id])
  replies         Message[] @relation("MessageReplies")

  // Legacy attachments field (for backward compatibility)
  attachments     Json?    // Array of file URLs

  // Audit fields
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // Relations
  messageAttachments MessageAttachment[]
  readReceipts    MessageReadReceipt[]

  @@map("messages")
  @@index([conversationId])
  @@index([senderId])
  @@index([receiverId])
  @@index([factoryId])
  @@index([createdAt])
  @@index([messageType])
  @@index([isDeleted])
  @@index([replyToId])
  // Legacy indexes for backward compatibility
  @@index([quoteId])
  @@index([orderId])
  @@index([inquiryId])
}

enum MessageType {
  TEXT
  IMAGE
  FILE
  SYSTEM
  VOICE           // Voice message
  VIDEO           // Video message
  LOCATION        // Location sharing
  CONTACT         // Contact sharing
}

model MessageAttachment {
  id              String   @id @default(cuid())

  // File information
  filename        String
  originalName    String   @map("original_name")
  mimeType        String   @map("mime_type")
  fileSize        Int      @map("file_size") // Size in bytes
  fileUrl         String   @map("file_url")
  thumbnailUrl    String?  @map("thumbnail_url") // For images/videos

  // File metadata
  width           Int?     // For images/videos
  height          Int?     // For images/videos
  duration        Int?     // For audio/video (in seconds)

  // Storage information
  storageProvider String   @default("supabase") @map("storage_provider")
  storagePath     String   @map("storage_path")
  bucketName      String   @map("bucket_name")

  // Message relationship
  messageId       String   @map("message_id")
  message         Message  @relation(fields: [messageId], references: [id], onDelete: Cascade)

  // Multi-tenant isolation
  factoryId       String   @map("factory_id")
  factory         Factory  @relation(fields: [factoryId], references: [id], onDelete: Cascade)

  // Upload tracking
  uploadStatus    UploadStatus @default(PENDING) @map("upload_status")
  uploadProgress  Int      @default(0) @map("upload_progress") // 0-100
  uploadError     String?  @map("upload_error")

  // Security and access
  isPublic        Boolean  @default(false) @map("is_public")
  accessToken     String?  @map("access_token") // For private files
  expiresAt       DateTime? @map("expires_at") // For temporary access

  // Audit fields
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  @@map("message_attachments")
  @@index([messageId])
  @@index([factoryId])
  @@index([uploadStatus])
  @@index([mimeType])
  @@index([createdAt])
}

enum UploadStatus {
  PENDING         // Upload initiated but not started
  UPLOADING       // Currently uploading
  PROCESSING      // Post-upload processing (thumbnails, etc.)
  COMPLETED       // Upload and processing complete
  FAILED          // Upload failed
  CANCELLED       // Upload cancelled
}

model MessageReadReceipt {
  id              String   @id @default(cuid())

  // Message and user relationship
  messageId       String   @map("message_id")
  message         Message  @relation(fields: [messageId], references: [id], onDelete: Cascade)
  userId          String   @map("user_id")
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Read status
  readAt          DateTime @default(now()) @map("read_at")

  // Multi-tenant isolation
  factoryId       String   @map("factory_id")
  factory         Factory  @relation(fields: [factoryId], references: [id], onDelete: Cascade)

  @@map("message_read_receipts")
  @@unique([messageId, userId])
  @@index([messageId])
  @@index([userId])
  @@index([factoryId])
  @@index([readAt])
}

// ============================================================================
// REVIEWS & RATINGS
// ============================================================================

model ProductReview {
  id          String   @id @default(cuid())
  rating      Int      // 1-5 stars
  title       String?
  comment     String?

  // Reviewer information
  reviewerName  String @map("reviewer_name")
  reviewerEmail String @map("reviewer_email")
  reviewerCompany String? @map("reviewer_company")

  // Product relationship
  productId   String   @map("product_id")
  product     Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  // Status
  isApproved  Boolean  @default(false) @map("is_approved")
  isVisible   Boolean  @default(true) @map("is_visible")

  // Audit fields
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("product_reviews")
  @@index([productId])
  @@index([rating])
  @@index([isApproved])
  @@index([createdAt])
}

// ============================================================================
// SYSTEM & AUDIT
// ============================================================================

model AuditLog {
  id          String   @id @default(cuid())
  type        AuditType
  event       String
  entityType  String?  @map("entity_type")
  entityId    String?  @map("entity_id")

  // User context
  userId      String?  @map("user_id")
  user        User?    @relation(fields: [userId], references: [id])

  // Factory context
  factoryId   String?  @map("factory_id")

  // Request context
  ipAddress   String?  @map("ip_address")
  userAgent   String?  @map("user_agent")

  // Event data
  metadata    Json?

  // Audit fields
  timestamp   DateTime @default(now())

  @@map("audit_logs")
  @@index([type])
  @@index([userId])
  @@index([factoryId])
  @@index([timestamp])
  @@index([entityType, entityId])
}

enum AuditType {
  SECURITY
  BUSINESS
  SYSTEM
}

model Notification {
  id          String   @id @default(cuid())
  type        NotificationType
  title       String
  message     String
  isRead      Boolean  @default(false) @map("is_read")
  readAt      DateTime? @map("read_at")

  // User relationship
  userId      String   @map("user_id")
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Notification data
  data        Json?

  // Audit fields
  createdAt   DateTime @default(now()) @map("created_at")

  @@map("notifications")
  @@index([userId])
  @@index([type])
  @@index([isRead])
  @@index([createdAt])
}

enum NotificationType {
  INFO
  SUCCESS
  WARNING
  ERROR
  ORDER_UPDATE
  MESSAGE
  SYSTEM
}

enum DocumentType {
  SPECIFICATION
  MANUAL
  CERTIFICATE
  BROCHURE
  OTHER
}
