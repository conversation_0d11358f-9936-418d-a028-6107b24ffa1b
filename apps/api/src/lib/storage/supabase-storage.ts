import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { config } from '../config';
import { logger } from '../logging/logger';
import { TRPCError } from '@trpc/server';
import sharp from 'sharp';
import { fileTypeFromBuffer } from 'file-type';
import crypto from 'crypto';
import path from 'path';

// Supabase Storage client for file operations
class SupabaseStorageService {
  private client: SupabaseClient;
  private readonly bucketName = 'fc-china-uploads';

  constructor() {
    this.client = createClient(config.SUPABASE_URL, config.SUPABASE_SERVICE_KEY, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    });
  }

  /**
   * Initialize storage bucket with proper configuration
   */
  async initializeBucket(): Promise<void> {
    try {
      // Check if bucket exists
      const { data: buckets, error: listError } = await this.client.storage.listBuckets();

      if (listError) {
        logger.error('Failed to list storage buckets', { error: listError });
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to access storage service',
        });
      }

      const bucketExists = buckets?.some(bucket => bucket.name === this.bucketName);

      if (!bucketExists) {
        logger.info('Creating storage bucket', { bucketName: this.bucketName });

        const { error: createError } = await this.client.storage.createBucket(this.bucketName, {
          public: true,
          fileSizeLimit: 52428800, // 50MB
          allowedMimeTypes: [
            // Images
            'image/jpeg',
            'image/png',
            'image/webp',
            'image/gif',
            'image/bmp',
            'image/svg+xml',
            // Documents
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain',
            'text/csv',
            // Videos (for future use)
            'video/mp4',
            'video/webm',
            'video/quicktime',
          ],
        });

        if (createError) {
          logger.error('Failed to create storage bucket', { error: createError });
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: 'Failed to initialize storage bucket',
          });
        }

        logger.info('Storage bucket created successfully', { bucketName: this.bucketName });
      }

      // Set up RLS policies (this would typically be done via SQL migrations)
      await this.setupStoragePolicies();

    } catch (error) {
      logger.error('Storage initialization failed', { error });
      throw error;
    }
  }

  /**
   * Generate a signed upload URL for direct client uploads
   */
  async generateUploadUrl(
    factoryId: string,
    uploadType: 'message' | 'product' | 'factory' | 'document',
    fileName: string,
    fileSize: number,
    mimeType: string,
    metadata?: Record<string, any>
  ): Promise<{
    uploadUrl: string;
    filePath: string;
    publicUrl: string;
    uploadId: string;
  }> {
    try {
      // Validate file type
      this.validateFileType(mimeType);

      // Validate file size
      this.validateFileSize(fileSize, mimeType);

      // Generate unique file path
      const uploadId = crypto.randomUUID();
      const fileExtension = path.extname(fileName) || this.getExtensionFromMimeType(mimeType);
      const sanitizedFileName = this.sanitizeFileName(path.basename(fileName, path.extname(fileName)));
      const timestamp = Date.now();

      let filePath: string;

      switch (uploadType) {
        case 'message':
          filePath = `messages/${factoryId}/${uploadId}/${timestamp}-${sanitizedFileName}${fileExtension}`;
          break;
        case 'product':
          const productId = metadata?.productId || 'temp';
          filePath = `products/${factoryId}/${productId}/${timestamp}-${sanitizedFileName}${fileExtension}`;
          break;
        case 'factory':
          filePath = `factory/${factoryId}/${uploadType}/${timestamp}-${sanitizedFileName}${fileExtension}`;
          break;
        case 'document':
          filePath = `documents/${factoryId}/${timestamp}-${sanitizedFileName}${fileExtension}`;
          break;
        default:
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Invalid upload type',
          });
      }

      // Generate signed upload URL (valid for 1 hour)
      const { data, error } = await this.client.storage
        .from(this.bucketName)
        .createSignedUploadUrl(filePath, {
          upsert: false,
        });

      if (error) {
        logger.error('Failed to generate upload URL', { error, filePath });
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to generate upload URL',
        });
      }

      const publicUrl = this.client.storage
        .from(this.bucketName)
        .getPublicUrl(filePath).data.publicUrl;

      logger.info('Generated upload URL', {
        filePath,
        uploadType,
        factoryId,
        fileSize,
        mimeType
      });

      return {
        uploadUrl: data.signedUrl,
        filePath,
        publicUrl,
        uploadId,
      };

    } catch (error) {
      logger.error('Upload URL generation failed', { error });
      throw error;
    }
  }

  /**
   * Confirm file upload and process the file
   */
  async confirmUpload(
    filePath: string,
    factoryId: string,
    uploadType: string,
    metadata?: Record<string, any>
  ): Promise<{
    success: boolean;
    fileInfo: {
      path: string;
      publicUrl: string;
      size: number;
      mimeType: string;
      thumbnailUrl?: string;
      metadata?: Record<string, any>;
    };
  }> {
    try {
      // Verify file exists in storage
      const { data: fileData, error: fileError } = await this.client.storage
        .from(this.bucketName)
        .list(path.dirname(filePath), {
          search: path.basename(filePath),
        });

      if (fileError || !fileData?.length) {
        logger.error('File not found in storage', { filePath, error: fileError });
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'File not found in storage',
        });
      }

      const file = fileData[0];
      const publicUrl = this.client.storage
        .from(this.bucketName)
        .getPublicUrl(filePath).data.publicUrl;

      let thumbnailUrl: string | undefined;
      let processedMetadata = metadata || {};

      // Process image files
      if (file.metadata?.mimetype?.startsWith('image/')) {
        const result = await this.processImage(filePath, factoryId);
        thumbnailUrl = result.thumbnailUrl;
        processedMetadata = { ...processedMetadata, ...result.metadata };
      }

      logger.info('File upload confirmed and processed', {
        filePath,
        size: file.metadata?.size,
        mimeType: file.metadata?.mimetype,
        hasThumbnail: !!thumbnailUrl,
      });

      return {
        success: true,
        fileInfo: {
          path: filePath,
          publicUrl,
          size: file.metadata?.size || 0,
          mimeType: file.metadata?.mimetype || 'application/octet-stream',
          thumbnailUrl,
          metadata: processedMetadata,
        },
      };

    } catch (error) {
      logger.error('File upload confirmation failed', { error, filePath });
      throw error;
    }
  }

  /**
   * Delete a file from storage
   */
  async deleteFile(filePath: string, factoryId: string): Promise<{ success: boolean }> {
    try {
      // Verify factory access (file path should start with factory ID)
      if (!filePath.includes(factoryId)) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied to file',
        });
      }

      const { error } = await this.client.storage
        .from(this.bucketName)
        .remove([filePath]);

      if (error) {
        logger.error('Failed to delete file', { error, filePath });
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to delete file',
        });
      }

      // Also delete thumbnail if it exists
      const thumbnailPath = this.getThumbnailPath(filePath);
      await this.client.storage
        .from(this.bucketName)
        .remove([thumbnailPath]);

      logger.info('File deleted successfully', { filePath });

      return { success: true };

    } catch (error) {
      logger.error('File deletion failed', { error, filePath });
      throw error;
    }
  }

  /**
   * Get file metadata and access URL
   */
  async getFileInfo(filePath: string, factoryId: string): Promise<{
    exists: boolean;
    publicUrl?: string;
    size?: number;
    mimeType?: string;
    lastModified?: string;
    thumbnailUrl?: string;
  }> {
    try {
      // Verify factory access
      if (!filePath.includes(factoryId)) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied to file',
        });
      }

      const { data: fileData, error } = await this.client.storage
        .from(this.bucketName)
        .list(path.dirname(filePath), {
          search: path.basename(filePath),
        });

      if (error) {
        logger.error('Failed to get file info', { error, filePath });
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get file information',
        });
      }

      if (!fileData?.length) {
        return { exists: false };
      }

      const file = fileData[0];
      const publicUrl = this.client.storage
        .from(this.bucketName)
        .getPublicUrl(filePath).data.publicUrl;

      let thumbnailUrl: string | undefined;
      if (file.metadata?.mimetype?.startsWith('image/')) {
        const thumbnailPath = this.getThumbnailPath(filePath);
        const { data: thumbnailData } = await this.client.storage
          .from(this.bucketName)
          .list(path.dirname(thumbnailPath), {
            search: path.basename(thumbnailPath),
          });

        if (thumbnailData?.length) {
          thumbnailUrl = this.client.storage
            .from(this.bucketName)
            .getPublicUrl(thumbnailPath).data.publicUrl;
        }
      }

      return {
        exists: true,
        publicUrl,
        size: file.metadata?.size,
        mimeType: file.metadata?.mimetype,
        lastModified: file.updated_at,
        thumbnailUrl,
      };

    } catch (error) {
      logger.error('Get file info failed', { error, filePath });
      throw error;
    }
  }

  /**
   * Process image file - generate thumbnails and extract metadata
   */
  private async processImage(filePath: string, factoryId: string): Promise<{
    thumbnailUrl: string;
    metadata: Record<string, any>;
  }> {
    try {
      // Download the original image
      const { data: imageBuffer, error } = await this.client.storage
        .from(this.bucketName)
        .download(filePath);

      if (error || !imageBuffer) {
        throw new Error('Failed to download image for processing');
      }

      const buffer = Buffer.from(await imageBuffer.arrayBuffer());

      // Extract image metadata
      const metadata = await sharp(buffer).metadata();

      // Generate thumbnail (300x300 max, maintain aspect ratio)
      const thumbnailBuffer = await sharp(buffer)
        .resize(300, 300, {
          fit: 'inside',
          withoutEnlargement: true,
        })
        .jpeg({ quality: 80 })
        .toBuffer();

      // Upload thumbnail
      const thumbnailPath = this.getThumbnailPath(filePath);
      const { error: uploadError } = await this.client.storage
        .from(this.bucketName)
        .upload(thumbnailPath, thumbnailBuffer, {
          contentType: 'image/jpeg',
          upsert: true,
        });

      if (uploadError) {
        logger.warn('Failed to upload thumbnail', { error: uploadError, filePath });
      }

      const thumbnailUrl = this.client.storage
        .from(this.bucketName)
        .getPublicUrl(thumbnailPath).data.publicUrl;

      return {
        thumbnailUrl,
        metadata: {
          width: metadata.width,
          height: metadata.height,
          format: metadata.format,
          size: metadata.size,
          hasAlpha: metadata.hasAlpha,
          orientation: metadata.orientation,
        },
      };

    } catch (error) {
      logger.error('Image processing failed', { error, filePath });
      // Return empty result if processing fails - don't block the upload
      return {
        thumbnailUrl: '',
        metadata: {},
      };
    }
  }

  /**
   * Set up Row Level Security policies for storage
   */
  private async setupStoragePolicies(): Promise<void> {
    // Note: In a real implementation, these policies would be set up via SQL migrations
    // This is just for documentation purposes
    logger.info('Storage RLS policies should be configured via SQL migrations');
  }

  /**
   * Validate file type against allowed types
   */
  private validateFileType(mimeType: string): void {
    const allowedTypes = [
      // Images
      'image/jpeg', 'image/png', 'image/webp', 'image/gif', 'image/bmp', 'image/svg+xml',
      // Documents
      'application/pdf', 'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain', 'text/csv',
      // Videos
      'video/mp4', 'video/webm', 'video/quicktime',
    ];

    if (!allowedTypes.includes(mimeType)) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: `File type ${mimeType} is not allowed`,
      });
    }
  }

  /**
   * Validate file size based on type
   */
  private validateFileSize(fileSize: number, mimeType: string): void {
    const maxSizes = {
      image: 10 * 1024 * 1024, // 10MB for images
      document: 50 * 1024 * 1024, // 50MB for documents
      video: 100 * 1024 * 1024, // 100MB for videos
    };

    let maxSize = maxSizes.document; // Default

    if (mimeType.startsWith('image/')) {
      maxSize = maxSizes.image;
    } else if (mimeType.startsWith('video/')) {
      maxSize = maxSizes.video;
    }

    if (fileSize > maxSize) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: `File size ${Math.round(fileSize / 1024 / 1024)}MB exceeds maximum allowed size of ${Math.round(maxSize / 1024 / 1024)}MB`,
      });
    }
  }

  /**
   * Sanitize filename to prevent security issues
   */
  private sanitizeFileName(fileName: string): string {
    return fileName
      .replace(/[^a-zA-Z0-9.-]/g, '_') // Replace special chars with underscore
      .replace(/_{2,}/g, '_') // Replace multiple underscores with single
      .replace(/^_+|_+$/g, '') // Remove leading/trailing underscores
      .substring(0, 100); // Limit length
  }

  /**
   * Get file extension from MIME type
   */
  private getExtensionFromMimeType(mimeType: string): string {
    const extensions: Record<string, string> = {
      'image/jpeg': '.jpg',
      'image/png': '.png',
      'image/webp': '.webp',
      'image/gif': '.gif',
      'image/bmp': '.bmp',
      'image/svg+xml': '.svg',
      'application/pdf': '.pdf',
      'application/msword': '.doc',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
      'application/vnd.ms-excel': '.xls',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
      'text/plain': '.txt',
      'text/csv': '.csv',
      'video/mp4': '.mp4',
      'video/webm': '.webm',
      'video/quicktime': '.mov',
    };

    return extensions[mimeType] || '';
  }

  /**
   * Generate thumbnail path from original file path
   */
  private getThumbnailPath(originalPath: string): string {
    const dir = path.dirname(originalPath);
    const ext = path.extname(originalPath);
    const name = path.basename(originalPath, ext);
    return `${dir}/thumbnails/${name}_thumb.jpg`;
  }
}

// Export singleton instance
export const supabaseStorage = new SupabaseStorageService();

// Export types
export interface FileUploadResult {
  success: boolean;
  filePath: string;
  publicUrl: string;
  thumbnailUrl?: string;
  size: number;
  mimeType: string;
  metadata?: Record<string, any>;
}

export interface UploadUrlResult {
  uploadUrl: string;
  filePath: string;
  publicUrl: string;
  uploadId: string;
}